<div class="min-h-screen bg-gray-50 p-6">
  <div class="max-w-full mx-auto">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-2">
        Pigeon Character Classification Results
      </h1>
    </div>

    <!-- Loading State -->
    @if (loading()) {
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-lg text-gray-600">Loading classification data...</span>
      </div>
    } @else {

      <!-- Character Groups -->
      @for (group of characterGroups(); track group.character) {
        <div class="mb-8">
          <!-- Character Header -->
          <div class="mb-4">
            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
              <span
                class="inline-block w-4 h-4 rounded mr-3"
                [style.background-color]="getCharacterColorHex(group.character)">
              </span>
              {{ getCharacterDisplayName(group.character) }}
              <span class="ml-2 text-sm font-normal text-gray-500">({{ group.pigeons.length }} pigeons)</span>
            </h2>
          </div>

          <!-- Pigeon Images Grid -->
          <div class="flex flex-wrap gap-2">
            @for (pigeon of group.pigeons; track pigeon.filename) {
              <div class="relative group">
                <img
                  [src]="pigeon.imageUrl"
                  [alt]="pigeon.filename"
                  class="pigeon-image w-24 h-24 object-cover rounded border-2 border-gray-200 hover:border-blue-400 transition-all cursor-pointer"
                  (error)="onImageError($event)"
                  (mouseenter)="onPigeonHover(pigeon, $event)"
                  (mouseleave)="onPigeonLeave()"
                  loading="lazy">

                <!-- Simple filename tooltip -->
                <!-- <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
                  {{ pigeon.filename }}
                </div> -->
              </div>
            }
          </div>
        </div>
      }
    }

    <!-- Hover Panel for Pigeon Details -->
    @if (hoveredPigeon() && showHoverPanel()) {
      <div
        class="hover-panel absolute right-40 top-10 bg-white rounded-lg border border-gray-200 p-4 z-50 max-w-md w-80 pointer-events-none">

        <!-- Large Image -->
        <div class="mb-3">
          <img
            [src]="hoveredPigeon()!.imageUrl"
            [alt]="hoveredPigeon()!.filename"
            class="w-full h-48 object-cover rounded border"
            (error)="onImageError($event)">
        </div>

        <!-- Filename -->
        <h3 class="font-semibold text-gray-900 mb-2 text-sm">{{ hoveredPigeon()!.filename }}</h3>

        <!-- Character Classification -->
        <div class="mb-3">
          <div class="flex items-center mb-1">
            <span class="inline-block w-3 h-3 rounded mr-2" [style.background-color]="getCharacterColorHex(hoveredPigeon()!.character)"></span>
            <span class="text-sm font-medium text-gray-700">{{ getCharacterDisplayName(hoveredPigeon()!.character) }}</span>
          </div>
        </div>

        <!-- Analysis Results -->
        <div class="space-y-2 text-xs">
          <!-- Species & Basic Info -->
          <div class="grid grid-cols-2 gap-2">
            <div>
              <span class="font-medium text-gray-600">Species:</span>
              <span class="ml-1 text-gray-800">{{ hoveredPigeon()!.analysis.species }}</span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Distinctiveness:</span>
              <span class="trait-badge distinctiveness ml-1"
                    [class]="hoveredPigeon()!.analysis.distinctiveness">
                {{ hoveredPigeon()!.analysis.distinctiveness }}
              </span>
            </div>
          </div>

          <!-- Description -->
          <div>
            <span class="font-medium text-gray-600">Description:</span>
            <p class="text-gray-800 text-xs mt-1 leading-relaxed">{{ hoveredPigeon()!.analysis.description }}</p>
          </div>

          <!-- Pigeon Traits (if available) -->
          @if (hoveredPigeon()!.analysis.pigeon_traits) {
            <div class="border-t pt-2 mt-2">
              <span class="font-medium text-gray-600 block mb-1">Traits:</span>
              <div class="grid grid-cols-2 gap-1 text-xs">
                <div>
                  <span class="text-gray-500">Base:</span>
                  <span class="ml-1 trait-badge base-color" [class]="hoveredPigeon()!.analysis.pigeon_traits!.base_color">
                    {{ hoveredPigeon()!.analysis.pigeon_traits!.base_color }}
                  </span>
                </div>
                <div>
                  <span class="text-gray-500">Pattern:</span>
                  <span class="ml-1 trait-badge pattern" [class]="hoveredPigeon()!.analysis.pigeon_traits!.main_pattern">
                    {{ hoveredPigeon()!.analysis.pigeon_traits!.main_pattern }}
                  </span>
                </div>
                @if (hoveredPigeon()!.analysis.pigeon_traits!.is_spread) {
                  <div class="col-span-2">
                    <span class="text-gray-500">Spread:</span>
                    <span class="ml-1 trait-badge spread">{{ hoveredPigeon()!.analysis.pigeon_traits!.spread_level }}</span>
                  </div>
                }
                @if (hoveredPigeon()!.analysis.pigeon_traits) {
                  <div class="col-span-2">
                    <span class="text-gray-500">Piebald:</span>
                    <span class="ml-1 trait-badge piebald">{{ hoveredPigeon()!.analysis.pigeon_traits!.piebald_level }}</span>
                    @if (hoveredPigeon()!.analysis.pigeon_traits!.piebald_pattern !== 'none') {
                      <span class="ml-1 trait-badge piebald-pattern">{{ hoveredPigeon()!.analysis.pigeon_traits!.piebald_pattern }}</span>
                    }
                  </div>
                }
                @if (hoveredPigeon()!.analysis.pigeon_traits!.head_pattern !== 'none') {
                  <div class="col-span-2">
                    <span class="text-gray-500">Head:</span>
                    <span class="ml-1 trait-badge head-pattern">{{ hoveredPigeon()!.analysis.pigeon_traits!.head_pattern }}</span>
                  </div>
                }
              </div>
            </div>
          }
        </div>
      </div>
    }
  </div>
</div>
