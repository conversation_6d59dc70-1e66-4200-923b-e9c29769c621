import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Subject } from 'rxjs';
import allPigeonsClassified from '../../services/classified';

@Component({
  selector: 'app-pigeon-classifier-visualization',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatChipsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatBadgeModule,
    MatTooltipModule,
  ],
  templateUrl: './pigeon-classifier-visualization.component.html',
  styleUrls: ['./pigeon-classifier-visualization.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PigeonClassifierVisualizationComponent
  implements OnInit, OnDestroy
{
  private destroy$ = new Subject<void>();

  // Signals for reactive state management
  loading = signal<boolean>(false);
  allPigeons = signal<ClassifiedPigeon[]>([]);
  filteredPigeons = signal<ClassifiedPigeon[]>([]);
  characterDistribution = signal<CharacterDistribution>({});
  uniqueCharacters = signal<string[]>([]);
  uniqueDistinctiveness = signal<string[]>([]);

  // Filter signals
  selectedCharacter = signal<string>('');
  selectedDistinctiveness = signal<string>('');
  searchQuery = signal<string>('');
  sortBy = signal<'character' | 'distinctiveness' | 'filename'>('character');

  // Hover state
  hoveredPigeon = signal<ClassifiedPigeon | null>(null);
  showHoverPanel = signal<boolean>(false);
  hoverPanelPosition = signal<{ x: number; y: number }>({ x: 0, y: 0 });

  // Computed properties
  characterGroups = computed(() => {
    const pigeons = this.filteredPigeons();
    const groups = new Map<string, ClassifiedPigeon[]>();

    pigeons.forEach((pigeon) => {
      if (!groups.has(pigeon.character)) {
        groups.set(pigeon.character, []);
      }
      groups.get(pigeon.character)!.push(pigeon);
    });

    // Convert to array and sort by character name
    return Array.from(groups.entries())
      .map(([character, pigeons]) => ({ character, pigeons }))
      .sort((a, b) => a.character.localeCompare(b.character));
  });

  totalPigeons = computed(() => this.filteredPigeons().length);
  totalCharacters = computed(() => this.characterGroups().length);

  constructor(private pigeonClassifierService: PigeonClassifierService) {}

  ngOnInit(): void {
    this.loading.set(true);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private applyFilters(): void {
    let filtered = [...this.allPigeons()];

    // Apply character filter
    if (this.selectedCharacter()) {
      filtered = filtered.filter(
        (p) => p.character === this.selectedCharacter()
      );
    }

    // Apply distinctiveness filter
    if (this.selectedDistinctiveness()) {
      filtered = filtered.filter(
        (p) => p.analysis.distinctiveness === this.selectedDistinctiveness()
      );
    }

    // Apply search filter
    if (this.searchQuery()) {
      const query = this.searchQuery().toLowerCase();
      filtered = filtered.filter(
        (p) =>
          p.filename.toLowerCase().includes(query) ||
          p.description.toLowerCase().includes(query) ||
          p.character.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    const sortBy = this.sortBy();
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'character':
          return a.character.localeCompare(b.character);
        case 'distinctiveness':
          return a.analysis.distinctiveness.localeCompare(
            b.analysis.distinctiveness
          );
        case 'filename':
          return a.filename.localeCompare(b.filename);
        default:
          return 0;
      }
    });

    this.filteredPigeons.set(filtered);
  }

  onCharacterFilterChange(character: string): void {
    this.selectedCharacter.set(character);
    this.applyFilters();
  }

  onDistinctivenessFilterChange(distinctiveness: string): void {
    this.selectedDistinctiveness.set(distinctiveness);
    this.applyFilters();
  }

  onSearchQueryChange(query: string): void {
    this.searchQuery.set(query);
    this.applyFilters();
  }

  onSortChange(sortBy: 'character' | 'distinctiveness' | 'filename'): void {
    this.sortBy.set(sortBy);
    this.applyFilters();
  }

  clearFilters(): void {
    this.selectedCharacter.set('');
    this.selectedDistinctiveness.set('');
    this.searchQuery.set('');
    this.applyFilters();
  }

  getCharacterDisplayName(character: string): string {
    return this.pigeonClassifierService.getCharacterDisplayName(character);
  }

  getCharacterColor(character: string): string {
    return this.pigeonClassifierService.getCharacterColor(character);
  }

  getCharacterColorHex(character: string): string {
    const colorMap: { [key: string]: string } = {
      blue: '#3B82F6',
      brown: '#A16207',
      red: '#DC2626',
      gray: '#6B7280',
      purple: '#9333EA',
      green: '#059669',
      orange: '#EA580C',
      indigo: '#4F46E5',
    };

    const color = this.getCharacterColor(character);
    return colorMap[color] || colorMap['indigo'];
  }

  onPigeonHover(pigeon: ClassifiedPigeon, event: MouseEvent): void {
    this.hoveredPigeon.set(pigeon);
    this.showHoverPanel.set(true);

    // Position the hover panel
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    const x = rect.right + 10;
    const y = rect.top;

    this.hoverPanelPosition.set({ x, y });
  }

  onPigeonLeave(): void {
    this.showHoverPanel.set(false);
    setTimeout(() => {
      if (!this.showHoverPanel()) {
        this.hoveredPigeon.set(null);
      }
    }, 100);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    img.src = 'assets/placeholder-pigeon.png';
  }
}
