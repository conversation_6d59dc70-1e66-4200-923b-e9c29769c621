.pigeon-image {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.hover-panel {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.trait-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;

  &.distinctiveness {
    &.common {
      background-color: #dcfce7;
      color: #166534;
    }
    &.unusual {
      background-color: #fef3c7;
      color: #92400e;
    }
    &.rare {
      background-color: #fecaca;
      color: #991b1b;
    }
  }

  &.base-color {
    &.blue {
      background-color: #dbeafe;
      color: #1e40af;
    }
    &.brown {
      background-color: #fef3c7;
      color: #a16207;
    }
    &.ash-red {
      background-color: #fecaca;
      color: #991b1b;
    }
    &.other {
      background-color: #f3f4f6;
      color: #374151;
    }
  }

  &.pattern {
    &.bar {
      background-color: #e0e7ff;
      color: #3730a3;
    }
    &.checker {
      background-color: #f3e8ff;
      color: #7c2d12;
    }
    &.t-check {
      background-color: #fce7f3;
      color: #be185d;
    }
    &.barless {
      background-color: #ccfbf1;
      color: #0f766e;
    }
    &.other {
      background-color: #f3f4f6;
      color: #374151;
    }
  }

  &.spread {
    background-color: #f3f4f6;
    color: #374151;
  }

  &.piebald {
    background-color: #f3e8ff;
    color: #7c2d12;
  }

  &.piebald-pattern {
    background-color: #ede9fe;
    color: #6b21a8;
  }

  &.head-pattern {
    background-color: #fed7aa;
    color: #c2410c;
  }
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.character-card {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.pigeon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 0.5rem;
}

.stats-card {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
}

.filter-section {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.character-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.character-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  display: flex;
  align-items: center;
}

.character-count {
  font-size: 0.875rem;
  color: #6b7280;
  background-color: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.empty-state {
  text-align: center;
  padding: 3rem 0;
  color: #6b7280;
}

// Responsive adjustments
@media (max-width: 768px) {
  .character-grid {
    grid-template-columns: 1fr;
  }
  
  .pigeon-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  }
  
  .hover-panel {
    position: fixed !important;
    left: 10px !important;
    right: 10px !important;
    bottom: 10px !important;
    top: auto !important;
    width: auto !important;
    max-width: none !important;
  }
}
