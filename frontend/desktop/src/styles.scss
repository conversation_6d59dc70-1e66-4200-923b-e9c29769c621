/* Global styles for Pigeon Analyzer */
@import "tailwindcss";
// Base styles
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: Robot<PERSON>, "Helvetica Neue", sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

// Typography enhancements
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5em;
  font-weight: 500;
  line-height: 1.2;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

// Utility classes
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

// Custom Material Design overrides
.mat-mdc-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  }
}

.mat-mdc-button {
  border-radius: 8px;
  font-weight: 500;
  text-transform: none;
}

.mat-mdc-raised-button {
  border-radius: 8px;
  font-weight: 500;
  text-transform: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  &:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }
}

.mat-mdc-form-field {
  .mat-mdc-form-field-wrapper {
    border-radius: 8px;
  }
}

// Loading animations
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.pulse {
  animation: pulse 2s infinite;
}

// Gang color scheme
.gang-one { color: #f44336; }
.gang-two { color: #2196f3; }
.gang-three { color: #4caf50; }
.gang-four { color: #ff9800; }
.gang-five { color: #9c27b0; }

.gang-one-bg { background-color: #ffebee; }
.gang-two-bg { background-color: #e3f2fd; }
.gang-three-bg { background-color: #e8f5e8; }
.gang-four-bg { background-color: #fff3e0; }
.gang-five-bg { background-color: #f3e5f5; }

// Distinctiveness colors
.distinctiveness-common { color: #666; }
.distinctiveness-unusual { color: #f57c00; }
.distinctiveness-rare { color: #c2185b; }

.distinctiveness-common-bg { background-color: #f5f5f5; }
.distinctiveness-unusual-bg { background-color: #fff8e1; }
.distinctiveness-rare-bg { background-color: #fce4ec; }

// Responsive breakpoints
$mobile: 768px;
$tablet: 1024px;
$desktop: 1200px;

// Scrollbar styling
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;

  &:hover {
    background: #a8a8a8;
  }
}
