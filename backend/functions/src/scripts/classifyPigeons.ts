/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-len */
import * as admin from "firebase-admin";
import serviceAccount from "../firebaseServiceAccount";

import fs from "fs";
import path from "path";
import { writeFile } from "fs/promises";
import { PigeonCharacterClassifier } from "../domain/services/PigeonCharacterClassifier";

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

async function classifyPigeons() {

    const resultsPath = path.resolve(__dirname, "../scripts/analysis_results.json");
    const analysisResultsAsJson = fs.readFileSync(resultsPath, "utf8");
    const analysisResults = JSON.parse(analysisResultsAsJson);
    
    for (const pigeon of analysisResults) {
        const character = PigeonCharacterClassifier.classifyPigeon(pigeon);
        console.log(`Pigeon ${pigeon.filename} classified as ${character}`);
        pigeon.character = character;
    }

    const analysisResultsPath = path.resolve(__dirname, "../tmp/analysis_results.json");
    await writeFile(analysisResultsPath, JSON.stringify(analysisResults, null, 2), "utf8");

    try {
    } catch (error) {
        console.error("Error adding pigeon:", error);
    } finally {
        await admin.app().delete();
    }
}

classifyPigeons();
